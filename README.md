# 🏖️ Holiday Adventure Dice Game

An interactive digital board game designed for ESL students learning Unit 15: "Our Holiday!" This game reinforces vocabulary and Future Simple grammar using "going to" structure.

## 🎯 Features

- **Interactive Board Game**: Visual SVG-based game board with animated player pieces
- **Vocabulary Practice**: 30+ holiday-related words from the lesson
- **Grammar Focus**: Future Simple using "going to" in positive, negative, and question forms
- **Smart Trap System**: 5 randomly placed trap squares that send players back 2 spaces
- **Anti-Double-Trap**: Players cannot be sent back twice consecutively
- **Multiplayer Support**: 2-6 players can play simultaneously
- **Visual Feedback**: Color-coded challenges and animated dice rolling
- **Game Log**: Track all game events and player progress

## 🚀 How to Use

1. **Setup**: Open `holiday-board-game.html` in any modern web browser
2. **Start Game**: Select number of players (2-6) and click "Start Game"
3. **Gameplay**: 
   - Current player rolls the dice
   - Move your piece forward the indicated number of spaces
   - Complete the language challenge based on the word you land on
   - Follow the sentence type instruction (positive, negative, or question)
   - Click "Complete Challenge" to end your turn
4. **Win Condition**: First player to reach the FINISH square wins!

## 🎲 Game Rules

### Movement
- Roll dice (1-6) and move forward that many spaces
- Follow the arrow path from START to FINISH
- Cannot move backward except when hitting trap squares

### Language Challenges
When you land on a square, create a sentence using "going to" with the vocabulary word:

- **🟢 Positive**: "I'm going to pack my suitcase for the holiday."
- **🔴 Negative**: "She isn't going to forget her toothbrush."
- **🔵 Question**: "Are you going to take sun cream to the beach?"

### Special Squares
- **START**: Beginning position for all players
- **FINISH**: Goal square - first to reach wins!
- **Trap Squares (⚠️ -2)**: Send player back 2 spaces
- **Regular Squares**: Contain vocabulary words for challenges

## 📚 Educational Benefits

- **Active Learning**: Combines physical movement with language practice
- **Immediate Practice**: Real-time application of grammar structures
- **Vocabulary Reinforcement**: Repeated exposure to lesson vocabulary
- **Speaking Confidence**: Low-pressure environment for oral practice
- **Peer Learning**: Students learn from each other's responses

## 🛠️ Technical Requirements

- Modern web browser (Chrome, Firefox, Safari, Edge)
- No internet connection required (runs locally)
- Responsive design works on tablets and computers
- No additional software installation needed

## 📖 Vocabulary Included

**Core Lesson Words**: suitcase, sun cream, towel, soap, shampoo, hairbrush, toothbrush, toothpaste, book, cake, rain, car, DVD

**Extended Holiday Words**: beach, holiday, swimming, packing, clothes, camera, sunglasses, hat, sandals, passport, tickets, hotel, airplane, luggage, flip-flops, swimsuit, backpack

## 🎨 Customization

Teachers can easily modify the game by editing the JavaScript file:
- Add new vocabulary words to the `vocabulary` array
- Adjust the number of trap squares
- Change player colors
- Modify board size and layout

## 📱 Classroom Tips

1. **Preparation**: Test the game before class and ensure all devices can access it
2. **Group Management**: Assign roles (dice roller, sentence judge, etc.)
3. **Assessment**: Use the game log to track student progress
4. **Extension**: Have students create their own sentences after the game
5. **Differentiation**: Allow weaker students to work in pairs

## 🔧 Files Included

- `holiday-board-game.html` - Main game interface
- `game-styles.css` - Visual styling and animations
- `game-script.js` - Game logic and interactivity
- `enhanced-game-description.md` - Detailed educational description
- `README.md` - This instruction file

## 🎉 Have Fun Learning!

This game transforms grammar practice into an engaging, competitive activity that students will actually enjoy. The combination of movement, challenge, and peer interaction creates an ideal environment for language learning.

Perfect for ESL classrooms, language centers, or home learning environments!
