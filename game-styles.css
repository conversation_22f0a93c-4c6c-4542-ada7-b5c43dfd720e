* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial Black', Arial, sans-serif;
    font-weight: 900;
    background: white;
    min-height: 100vh;
    padding: 20px;
    color: #000000;
}

.game-container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border: 5px solid #000000;
    border-radius: 10px;
    overflow: hidden;
}

.game-header {
    background: white;
    color: #000000;
    text-align: center;
    padding: 20px;
    border-bottom: 5px solid #000000;
}

.game-header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 900;
}

.game-controls {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
    padding: 20px;
    background: white;
    border-bottom: 5px solid #000000;
}

.player-setup {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.player-setup select, .player-setup button {
    padding: 10px;
    border: 3px solid #000000;
    border-radius: 5px;
    font-size: 16px;
    font-weight: 900;
    background: white;
    color: #000000;
}

.player-setup button {
    background: #000000;
    color: white;
    cursor: pointer;
    transition: all 0.3s;
}

.player-setup button:hover {
    background: #333333;
    transform: translateY(-2px);
}

.current-player {
    text-align: center;
}

.dice-container {
    margin: 15px 0;
}

.dice {
    font-size: 4em;
    margin: 10px;
    display: inline-block;
    animation: bounce 0.5s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 60%, 100% { transform: translateY(0); }
    40% { transform: translateY(-20px); }
    80% { transform: translateY(-10px); }
}

.dice.rolling {
    animation: spin 0.5s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#roll-dice, #complete-challenge {
    background: #000000;
    color: white;
    border: 3px solid #000000;
    padding: 12px 24px;
    border-radius: 5px;
    font-size: 16px;
    font-weight: 900;
    cursor: pointer;
    transition: all 0.3s;
}

#roll-dice:hover, #complete-challenge:hover {
    background: #333333;
    transform: scale(1.05);
}

.challenge-display {
    background: white;
    border: 4px solid #000000;
    border-radius: 5px;
    padding: 15px;
}

.word-display {
    font-size: 2em;
    font-weight: 900;
    color: #000000;
    margin: 10px 0;
    text-align: center;
    background: white;
    padding: 15px;
    border-radius: 5px;
    border: 4px solid #000000;
}

.sentence-type {
    font-size: 1.5em;
    font-weight: 900;
    margin: 10px 0;
    text-align: center;
    padding: 10px;
    border-radius: 5px;
}

.sentence-type.positive {
    background: white;
    color: #000000;
    border: 4px solid #000000;
}

.sentence-type.negative {
    background: white;
    color: #000000;
    border: 4px solid #000000;
}

.sentence-type.question {
    background: white;
    color: #000000;
    border: 4px solid #000000;
}

.challenge-examples {
    font-size: 0.9em;
    font-weight: 900;
    margin: 10px 0;
    background: white;
    padding: 10px;
    border-radius: 5px;
    border: 3px solid #000000;
}

.game-board-container {
    padding: 20px;
    background: white;
}

.game-board {
    width: 100%;
    height: 600px;
    background: white;
    border-radius: 5px;
    border: 5px solid #000000;
}

.board-square {
    stroke: #000000;
    stroke-width: 4;
    cursor: pointer;
    transition: all 0.3s;
}

.board-square:hover {
    stroke-width: 5;
}

.start-square {
    fill: white;
}

.finish-square {
    fill: white;
}

.normal-square {
    fill: white;
}

.trap-square {
    fill: white;
}

.square-text {
    font-family: 'Arial Black', Arial, sans-serif;
    font-size: 12px;
    font-weight: 900;
    text-anchor: middle;
    dominant-baseline: middle;
    fill: #000000;
}

.player-piece {
    stroke: #000000;
    stroke-width: 3;
    transition: all 0.5s ease-in-out;
}

.game-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    padding: 20px;
    background: white;
}

.players-list, .game-log {
    background: white;
    padding: 15px;
    border-radius: 5px;
    border: 4px solid #000000;
    font-weight: 900;
}

.player-info {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;
    padding: 10px;
    border-radius: 5px;
    background: white;
    border: 3px solid #000000;
    font-weight: 900;
}

.player-info.current {
    background: white;
    border: 4px solid #000000;
}

.player-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 3px solid #000000;
}

.game-log {
    max-height: 200px;
    overflow-y: auto;
}

.log-message {
    margin: 5px 0;
    padding: 8px;
    border-radius: 5px;
    background: white;
    border: 2px solid #000000;
    font-weight: 900;
}

.winner-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    z-index: 1000;
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 40px;
    border-radius: 5px;
    border: 5px solid #000000;
    text-align: center;
}

.modal-content h2 {
    font-size: 3em;
    color: #000000;
    margin-bottom: 20px;
    font-weight: 900;
}

#new-game {
    background: #000000;
    color: white;
    border: 3px solid #000000;
    padding: 15px 30px;
    border-radius: 5px;
    font-size: 18px;
    font-weight: 900;
    cursor: pointer;
    margin-top: 20px;
}

@media (max-width: 768px) {
    .game-controls {
        grid-template-columns: 1fr;
    }
    
    .game-info {
        grid-template-columns: 1fr;
    }
    
    .game-header h1 {
        font-size: 2em;
    }
}
