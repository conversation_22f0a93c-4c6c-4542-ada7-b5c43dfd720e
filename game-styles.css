* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Comic Sans MS', cursive, sans-serif;
    background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
    min-height: 100vh;
    padding: 20px;
}

.game-container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    overflow: hidden;
}

.game-header {
    background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
    color: white;
    text-align: center;
    padding: 20px;
}

.game-header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.game-controls {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 3px solid #dee2e6;
}

.player-setup {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.player-setup select, .player-setup button {
    padding: 10px;
    border: 2px solid #4ECDC4;
    border-radius: 10px;
    font-size: 16px;
}

.player-setup button {
    background: #4ECDC4;
    color: white;
    cursor: pointer;
    transition: all 0.3s;
}

.player-setup button:hover {
    background: #45B7B8;
    transform: translateY(-2px);
}

.current-player {
    text-align: center;
}

.dice-container {
    margin: 15px 0;
}

.dice {
    font-size: 4em;
    margin: 10px;
    display: inline-block;
    animation: bounce 0.5s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 60%, 100% { transform: translateY(0); }
    40% { transform: translateY(-20px); }
    80% { transform: translateY(-10px); }
}

.dice.rolling {
    animation: spin 0.5s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#roll-dice, #complete-challenge {
    background: #FF6B6B;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s;
}

#roll-dice:hover, #complete-challenge:hover {
    background: #FF5252;
    transform: scale(1.05);
}

.challenge-display {
    background: #fff3cd;
    border: 2px solid #ffeaa7;
    border-radius: 15px;
    padding: 15px;
}

.word-display {
    font-size: 2em;
    font-weight: bold;
    color: #2d3436;
    margin: 10px 0;
    text-align: center;
    background: white;
    padding: 15px;
    border-radius: 10px;
    border: 3px solid #4ECDC4;
}

.sentence-type {
    font-size: 1.5em;
    font-weight: bold;
    margin: 10px 0;
    text-align: center;
    padding: 10px;
    border-radius: 10px;
}

.sentence-type.positive {
    background: #d4edda;
    color: #155724;
    border: 2px solid #28a745;
}

.sentence-type.negative {
    background: #f8d7da;
    color: #721c24;
    border: 2px solid #dc3545;
}

.sentence-type.question {
    background: #d1ecf1;
    color: #0c5460;
    border: 2px solid #17a2b8;
}

.challenge-examples {
    font-size: 0.9em;
    margin: 10px 0;
    background: white;
    padding: 10px;
    border-radius: 8px;
}

.game-board-container {
    padding: 20px;
    background: #f8f9fa;
}

.game-board {
    width: 100%;
    height: 600px;
    background: linear-gradient(45deg, #74b9ff, #0984e3);
    border-radius: 15px;
    border: 5px solid #2d3436;
}

.board-square {
    stroke: #2d3436;
    stroke-width: 2;
    cursor: pointer;
    transition: all 0.3s;
}

.board-square:hover {
    stroke-width: 3;
}

.start-square {
    fill: #00b894;
}

.finish-square {
    fill: #fdcb6e;
}

.normal-square {
    fill: #74b9ff;
}

.trap-square {
    fill: #e17055;
}

.square-text {
    font-family: 'Comic Sans MS', cursive;
    font-size: 12px;
    font-weight: bold;
    text-anchor: middle;
    dominant-baseline: middle;
    fill: #2d3436;
}

.player-piece {
    stroke: #2d3436;
    stroke-width: 2;
    transition: all 0.5s ease-in-out;
}

.game-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    padding: 20px;
    background: #e9ecef;
}

.players-list, .game-log {
    background: white;
    padding: 15px;
    border-radius: 10px;
    border: 2px solid #dee2e6;
}

.player-info {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;
    padding: 10px;
    border-radius: 8px;
    background: #f8f9fa;
}

.player-info.current {
    background: #d4edda;
    border: 2px solid #28a745;
}

.player-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #2d3436;
}

.game-log {
    max-height: 200px;
    overflow-y: auto;
}

.log-message {
    margin: 5px 0;
    padding: 8px;
    border-radius: 5px;
    background: #f8f9fa;
    border-left: 4px solid #4ECDC4;
}

.winner-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    z-index: 1000;
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.modal-content h2 {
    font-size: 3em;
    color: #FF6B6B;
    margin-bottom: 20px;
}

#new-game {
    background: #4ECDC4;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 18px;
    cursor: pointer;
    margin-top: 20px;
}

@media (max-width: 768px) {
    .game-controls {
        grid-template-columns: 1fr;
    }
    
    .game-info {
        grid-template-columns: 1fr;
    }
    
    .game-header h1 {
        font-size: 2em;
    }
}
