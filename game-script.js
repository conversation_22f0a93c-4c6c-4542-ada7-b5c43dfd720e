class HolidayBoardGame {
    constructor() {
        this.vocabulary = [
            'suitcase', 'sun cream', 'towel', 'soap', 'shampoo', 'hairbrush',
            'toothbrush', 'toothpaste', 'book', 'cake', 'rain', 'car', 'DVD',
            'beach', 'holiday', 'swimming', 'packing', 'clothes', 'camera',
            'sunglasses', 'hat', 'sandals', 'passport', 'tickets', 'hotel',
            'airplane', 'luggage', 'flip-flops', 'swimsuit', 'backpack'
        ];
        
        this.sentenceTypes = [
            { type: 'positive', symbol: '🟢', instruction: 'Make a POSITIVE sentence using "going to"' },
            { type: 'negative', symbol: '🔴', instruction: 'Make a NEGATIVE sentence using "going to"' },
            { type: 'question', symbol: '🔵', instruction: 'Make a QUESTION using "going to"' }
        ];
        
        this.playerColors = ['#FF6B6B', '#4ECDC4', '#45B7B8', '#96CEB4', '#FFEAA7', '#DDA0DD'];
        this.players = [];
        this.currentPlayerIndex = 0;
        this.boardSquares = [];
        this.trapSquares = [];
        this.gameStarted = false;
        
        this.initializeEventListeners();
        this.createBoard();
    }
    
    initializeEventListeners() {
        document.getElementById('start-game').addEventListener('click', () => this.startGame());
        document.getElementById('roll-dice').addEventListener('click', () => this.rollDice());
        document.getElementById('complete-challenge').addEventListener('click', () => this.completeChallenge());
        document.getElementById('new-game').addEventListener('click', () => this.resetGame());
    }
    
    startGame() {
        const playerCount = parseInt(document.getElementById('player-count').value);
        this.players = [];
        
        for (let i = 0; i < playerCount; i++) {
            this.players.push({
                id: i,
                name: `Player ${i + 1}`,
                position: 0,
                color: this.playerColors[i],
                hasWon: false
            });
        }
        
        this.currentPlayerIndex = 0;
        this.gameStarted = true;
        this.updatePlayersDisplay();
        this.updateCurrentPlayerDisplay();
        this.addLogMessage('Game started! Roll the dice to begin.');
        
        // Hide challenge display initially
        document.getElementById('challenge-display').style.display = 'none';
    }
    
    createBoard() {
        const svg = document.getElementById('game-board');
        const boardWidth = 800;
        const boardHeight = 600;
        const squareSize = 75; // Increased spacing to accommodate larger squares (25% larger)

        // Create board path (snake-like pattern)
        const path = this.generateBoardPath(boardWidth, boardHeight, squareSize);
        this.boardSquares = path;

        // Randomly select 5 trap positions (not start, finish, or consecutive)
        this.generateTrapSquares();

        // Draw arrows between squares first (so they appear behind squares)
        this.drawArrows(svg, path);

        // Draw squares
        path.forEach((square, index) => this.drawSquare(svg, square, index));

        // Draw player pieces
        this.drawPlayerPieces(svg);
    }
    
    generateBoardPath(width, height, squareSize) {
        const path = [];
        const cols = Math.floor(width / squareSize) - 1;
        const rows = Math.floor(height / squareSize) - 1;
        let x = 50, y = height - 100;
        let direction = 1; // 1 for right, -1 for left
        
        // Start square
        path.push({ x, y, type: 'start', word: 'START' });
        
        for (let row = 0; row < rows - 1; row++) {
            // Move horizontally
            for (let col = 1; col < cols; col++) {
                x += squareSize * direction;
                const wordIndex = (path.length - 1) % this.vocabulary.length;
                path.push({
                    x, y,
                    type: 'normal',
                    word: this.vocabulary[wordIndex]
                });
            }
            
            // Move up and change direction
            y -= squareSize;
            direction *= -1;
        }
        
        // Finish square
        path.push({ x, y, type: 'finish', word: 'FINISH' });
        
        return path;
    }
    
    generateTrapSquares() {
        const totalSquares = this.boardSquares.length;
        const trapCount = 5;
        this.trapSquares = [];
        
        // Ensure traps are not on start, finish, or consecutive positions
        const availablePositions = [];
        for (let i = 2; i < totalSquares - 2; i++) {
            availablePositions.push(i);
        }
        
        // Randomly select trap positions
        for (let i = 0; i < trapCount; i++) {
            if (availablePositions.length === 0) break;
            
            const randomIndex = Math.floor(Math.random() * availablePositions.length);
            const trapPosition = availablePositions[randomIndex];
            this.trapSquares.push(trapPosition);
            
            // Remove this position and adjacent positions
            availablePositions.splice(randomIndex, 1);
            const adjacentIndex1 = availablePositions.indexOf(trapPosition - 1);
            const adjacentIndex2 = availablePositions.indexOf(trapPosition + 1);
            if (adjacentIndex1 !== -1) availablePositions.splice(adjacentIndex1, 1);
            if (adjacentIndex2 !== -1) availablePositions.splice(adjacentIndex2, 1);
        }
    }
    
    drawSquare(svg, square, index) {
        // Make squares 25% larger: 50 * 1.25 = 62.5
        const squareSize = 62.5;
        const halfSize = squareSize / 2;

        const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        rect.setAttribute('x', square.x - halfSize);
        rect.setAttribute('y', square.y - halfSize);
        rect.setAttribute('width', squareSize);
        rect.setAttribute('height', squareSize);
        rect.setAttribute('rx', 8);

        let className = 'board-square ';
        if (square.type === 'start') className += 'start-square';
        else if (square.type === 'finish') className += 'finish-square';
        else if (this.trapSquares.includes(index)) className += 'trap-square';
        else className += 'normal-square';

        rect.setAttribute('class', className);
        svg.appendChild(rect);

        // Add text
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', square.x);
        text.setAttribute('y', square.y - 5);
        text.setAttribute('class', 'square-text');
        text.textContent = square.word;
        svg.appendChild(text);

        // Add trap symbol if it's a trap square
        if (this.trapSquares.includes(index)) {
            const trapText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            trapText.setAttribute('x', square.x);
            trapText.setAttribute('y', square.y + 15);
            trapText.setAttribute('class', 'square-text');
            trapText.setAttribute('font-size', '10');
            trapText.textContent = '⚠️ -2';
            svg.appendChild(trapText);
        }
    }
    
    drawArrows(svg, path) {
        // Create smaller arrowhead marker (65% smaller)
        const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
        const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
        marker.setAttribute('id', 'arrowhead');
        marker.setAttribute('markerWidth', '4.2');  // 65% smaller: 12 * 0.35 = 4.2
        marker.setAttribute('markerHeight', '2.8');  // 65% smaller: 8 * 0.35 = 2.8
        marker.setAttribute('refX', '3.5');  // 65% smaller: 10 * 0.35 = 3.5
        marker.setAttribute('refY', '1.4');  // 65% smaller: 4 * 0.35 = 1.4
        marker.setAttribute('orient', 'auto');

        const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
        polygon.setAttribute('points', '0 0, 4.2 1.4, 0 2.8');  // 65% smaller proportions
        polygon.setAttribute('fill', '#000000');
        polygon.setAttribute('stroke', '#000000');
        polygon.setAttribute('stroke-width', '0.7');  // 65% smaller: 2 * 0.35 = 0.7

        marker.appendChild(polygon);
        defs.appendChild(marker);
        svg.appendChild(defs);

        // Draw smaller arrows above the squares
        for (let i = 0; i < path.length - 1; i++) {
            const current = path[i];
            const next = path[i + 1];

            // Calculate arrow position above the squares
            const arrowY = Math.min(current.y, next.y) - 40;

            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('x1', current.x);
            line.setAttribute('y1', arrowY);
            line.setAttribute('x2', next.x);
            line.setAttribute('y2', arrowY);
            line.setAttribute('stroke', '#000000');
            line.setAttribute('stroke-width', '1.4');  // 65% smaller: 4 * 0.35 = 1.4
            line.setAttribute('marker-end', 'url(#arrowhead)');
            svg.appendChild(line);
        }
    }
    
    drawPlayerPieces(svg) {
        this.players.forEach((player, index) => {
            const piece = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            piece.setAttribute('id', `player-${player.id}`);
            piece.setAttribute('r', 8);
            piece.setAttribute('fill', player.color);
            piece.setAttribute('class', 'player-piece');
            
            this.updatePlayerPosition(piece, player.position, index);
            svg.appendChild(piece);
        });
    }
    
    updatePlayerPosition(piece, position, playerOffset = 0) {
        if (position >= this.boardSquares.length) {
            position = this.boardSquares.length - 1;
        }
        
        const square = this.boardSquares[position];
        const offsetX = (playerOffset % 2) * 16 - 8;
        const offsetY = Math.floor(playerOffset / 2) * 16 - 8;
        
        piece.setAttribute('cx', square.x + offsetX);
        piece.setAttribute('cy', square.y + offsetY);
    }
    
    rollDice() {
        if (!this.gameStarted) {
            this.addLogMessage('Please start the game first!');
            return;
        }
        
        const dice = document.getElementById('dice');
        const rollButton = document.getElementById('roll-dice');
        
        // Animate dice rolling
        dice.classList.add('rolling');
        rollButton.disabled = true;
        
        setTimeout(() => {
            const diceValue = Math.floor(Math.random() * 6) + 1;
            dice.textContent = diceValue;
            dice.classList.remove('rolling');
            
            document.getElementById('dice-result').textContent = `Rolled: ${diceValue}`;
            
            this.movePlayer(diceValue);
            rollButton.disabled = false;
        }, 1000);
    }
    
    movePlayer(steps) {
        const currentPlayer = this.players[this.currentPlayerIndex];
        const newPosition = Math.min(currentPlayer.position + steps, this.boardSquares.length - 1);
        
        currentPlayer.position = newPosition;
        
        // Update visual position
        const piece = document.getElementById(`player-${currentPlayer.id}`);
        this.updatePlayerPosition(piece, newPosition, this.currentPlayerIndex);
        
        this.addLogMessage(`${currentPlayer.name} moved to position ${newPosition + 1}`);
        
        // Check for win condition
        if (newPosition === this.boardSquares.length - 1) {
            this.playerWins(currentPlayer);
            return;
        }
        
        // Check for trap square
        if (this.trapSquares.includes(newPosition)) {
            this.handleTrapSquare(currentPlayer);
        } else {
            this.showChallenge(newPosition);
        }
    }
    
    handleTrapSquare(player) {
        const newPosition = Math.max(0, player.position - 2);
        player.position = newPosition;
        
        const piece = document.getElementById(`player-${player.id}`);
        this.updatePlayerPosition(piece, newPosition, this.currentPlayerIndex);
        
        this.addLogMessage(`${player.name} landed on a trap! Moved back 2 spaces to position ${newPosition + 1}`);
        
        // Still show challenge for the new position
        this.showChallenge(newPosition);
    }
    
    showChallenge(position) {
        const square = this.boardSquares[position];
        const randomSentenceType = this.sentenceTypes[Math.floor(Math.random() * this.sentenceTypes.length)];
        
        document.getElementById('word-display').textContent = square.word;
        
        const sentenceTypeDiv = document.getElementById('sentence-type');
        sentenceTypeDiv.textContent = `${randomSentenceType.symbol} ${randomSentenceType.instruction}`;
        sentenceTypeDiv.className = `sentence-type ${randomSentenceType.type}`;
        
        document.getElementById('challenge-display').style.display = 'block';
        document.getElementById('roll-dice').style.display = 'none';
    }
    
    completeChallenge() {
        document.getElementById('challenge-display').style.display = 'none';
        document.getElementById('roll-dice').style.display = 'inline-block';
        
        const currentPlayer = this.players[this.currentPlayerIndex];
        this.addLogMessage(`${currentPlayer.name} completed the challenge!`);
        
        // Move to next player
        this.currentPlayerIndex = (this.currentPlayerIndex + 1) % this.players.length;
        this.updateCurrentPlayerDisplay();
        this.updatePlayersDisplay();
    }
    
    playerWins(player) {
        player.hasWon = true;
        document.getElementById('winner-message').textContent = `${player.name} wins the Holiday Adventure!`;
        document.getElementById('winner-modal').style.display = 'block';
        this.addLogMessage(`🎉 ${player.name} reached the finish line and wins!`);
    }
    
    updateCurrentPlayerDisplay() {
        const currentPlayer = this.players[this.currentPlayerIndex];
        document.getElementById('current-player-name').textContent = currentPlayer.name;
        document.getElementById('current-player-name').style.color = currentPlayer.color;
    }
    
    updatePlayersDisplay() {
        const playersList = document.getElementById('players-list');
        playersList.innerHTML = '<h4>Players</h4>';
        
        this.players.forEach((player, index) => {
            const playerDiv = document.createElement('div');
            playerDiv.className = `player-info ${index === this.currentPlayerIndex ? 'current' : ''}`;
            
            playerDiv.innerHTML = `
                <div class="player-color" style="background-color: ${player.color}"></div>
                <span>${player.name}</span>
                <span>Position: ${player.position + 1}</span>
            `;
            
            playersList.appendChild(playerDiv);
        });
    }
    
    addLogMessage(message) {
        const logMessages = document.getElementById('log-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'log-message';
        messageDiv.textContent = message;
        
        logMessages.appendChild(messageDiv);
        logMessages.scrollTop = logMessages.scrollHeight;
    }
    
    resetGame() {
        document.getElementById('winner-modal').style.display = 'none';
        this.gameStarted = false;
        this.currentPlayerIndex = 0;
        this.players = [];
        
        // Clear the board and recreate it
        const svg = document.getElementById('game-board');
        svg.innerHTML = '';
        this.createBoard();
        
        // Reset displays
        document.getElementById('players-list').innerHTML = '<h4>Players</h4>';
        document.getElementById('log-messages').innerHTML = '';
        document.getElementById('challenge-display').style.display = 'none';
        document.getElementById('roll-dice').style.display = 'inline-block';
        document.getElementById('dice-result').textContent = '';
        document.getElementById('dice').textContent = '🎲';
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new HolidayBoardGame();
});
